# 借方金额和贷方金额文本框属性和CSS效果

## HTML 结构

### 借方金额文本框
```html
<input type="number" 
       class="cell-input amount-input debit-amount"
       value="{% if detail.debit_amount > 0 %}{{ detail.debit_amount }}{% endif %}"
       {% if mode == 'view' %}readonly{% endif %}
       step="0.01" 
       min="0" 
       placeholder="0.00">
```

### 贷方金额文本框
```html
<input type="number" 
       class="cell-input amount-input credit-amount"
       value="{% if detail.credit_amount > 0 %}{{ detail.credit_amount }}{% endif %}"
       {% if mode == 'view' %}readonly{% endif %}
       step="0.01" 
       min="0" 
       placeholder="0.00">
```

## HTML 属性详解

### 基础属性
- **type="number"**: 数字输入类型，支持数字键盘和验证
- **step="0.01"**: 步进值为0.01，支持小数点后两位
- **min="0"**: 最小值为0，不允许负数
- **placeholder="0.00"**: 占位符显示格式

### CSS 类名
- **cell-input**: 通用单元格输入框样式
- **amount-input**: 金额输入框专用样式
- **debit-amount**: 借方金额标识类
- **credit-amount**: 贷方金额标识类

### 动态属性
- **value**: 根据数据库值动态设置，大于0时显示
- **readonly**: 查看模式下为只读状态

## CSS 样式效果

### 1. 基础样式 (.cell-input)
```css
.cell-input {
    border: none;                    /* 无边框 */
    background: transparent;         /* 透明背景 */
    width: 100%;                    /* 全宽 */
    padding: 4px 6px;               /* 内边距 */
    font-size: 13px;                /* 字体大小 */
    outline: none;                  /* 无轮廓 */
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    transition: all 0.2s ease;     /* 过渡动画 */
}
```

### 2. 金额输入框样式 (.amount-input)
```css
.amount-input {
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;  /* 专业字体 */
    font-weight: bold;              /* 粗体 */
    text-align: right;              /* 右对齐 */
    font-size: 13px;                /* 字体大小 */
    color: #000;                    /* 黑色文字 */
    letter-spacing: 0.5px;          /* 字符间距 */
    width: 100%;                    /* 全宽 */
    padding: 4px 8px;               /* 内边距 */
    border: 1px solid transparent;  /* 透明边框 */
    background: transparent;         /* 透明背景 */
}
```

### 3. 焦点状态样式
```css
.cell-input:focus {
    background: #e3f2fd;           /* 浅蓝色背景 */
    border: 1px solid #1e88e5;    /* 蓝色边框 */
    border-radius: 2px;            /* 圆角 */
}

.amount-input:focus {
    background: #fff7e6;           /* 浅橙色背景 */
    border: 1px solid #faad14;    /* 橙色边框 */
    outline: none;                 /* 无轮廓 */
}
```

### 4. 列对齐样式
```css
.voucher-table td:nth-child(4),    /* 借方金额列 */
.voucher-table td:nth-child(5) {   /* 贷方金额列 */
    text-align: right;             /* 右对齐 */
    padding-right: 8px;            /* 右内边距 */
    border: none;                  /* 无边框（已删除表线条）*/
}
```

### 5. 列宽控制
```css
.col-debit,
.col-credit {
    width: 15%;                    /* 宽度15% */
    min-width: 100px;              /* 最小宽度100px */
    text-align: right;             /* 右对齐 */
}
```

## JavaScript 事件处理

### 1. 输入事件监听
```javascript
$(document).on('input', '.amount-input', function() {
    updateTotals();                /* 更新合计 */
    checkBalance();                /* 检查借贷平衡 */
});
```

### 2. 金额格式化函数
```javascript
function getAmountValue(value) {
    return parseFloat(value) || 0;
}

function formatAmount(amount) {
    return amount.toFixed(2);
}
```

### 3. 合计计算
```javascript
function updateTotals() {
    let debitTotal = 0;
    let creditTotal = 0;

    $('.debit-amount').each(function() {
        const value = getAmountValue($(this).val());
        debitTotal += value;
    });

    $('.credit-amount').each(function() {
        const value = getAmountValue($(this).val());
        creditTotal += value;
    });

    // 更新合计显示
    $('#debit-total').html('<span class="uf-currency">¥</span>' + formatAmount(debitTotal));
    $('#credit-total').html('<span class="uf-currency">¥</span>' + formatAmount(creditTotal));
}
```

## 用户交互特性

### 1. 键盘导航
- **Tab**: 移动到下一个输入框
- **Shift+Tab**: 移动到上一个输入框
- **Enter**: 移动到下一行相同列
- **方向键**: 上下左右导航

### 2. 数据验证
- 自动验证数字格式
- 限制最小值为0
- 支持小数点后两位
- 实时更新合计金额

### 3. 视觉反馈
- 焦点状态高亮显示
- 透明背景与表格融合
- 专业的用友风格字体
- 右对齐的金额显示

### 4. 响应式设计
- 移动端适配（最小宽度80px）
- 桌面端优化（最小宽度100px）
- 自适应列宽调整

## 特殊效果

### 1. 表线条删除
- 借方金额和贷方金额列的表格边框已被删除
- 保持其他列的边框显示
- 创造更清洁的视觉效果

### 2. 用友风格
- 专业的财务软件外观
- 经典的字体组合
- 标准的会计数字格式
- 符合财务人员使用习惯
